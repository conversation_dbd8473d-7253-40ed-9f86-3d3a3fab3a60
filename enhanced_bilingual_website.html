<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- SEO Meta Tags - English -->
    <title data-en="Solar AI Cleaning & Monitoring System - Revolutionary Solar Panel Maintenance" data-ar="نظام مراقبة وتنظيف الألواح الشمسية بالذكاء الاصطناعي - صيانة ثورية للألواح الشمسية">Solar AI Cleaning & Monitoring System</title>
    <meta name="description" data-en="Transform your solar operations with 99% AI detection accuracy, 50% cost reduction, and 85% water savings. Advanced AI-powered solar panel cleaning with 9 innovative methods." data-ar="حوّل عمليات الطاقة الشمسية لديك بدقة كشف 99% بالذكاء الاصطناعي، وتقليل التكاليف بنسبة 50%، وتوفير المياه بنسبة 85%. تنظيف متطور للألواح الشمسية بـ 9 طرق مبتكرة." content="Transform your solar operations with 99% AI detection accuracy, 50% cost reduction, and 85% water savings.">
    <meta name="keywords" data-en="solar panel cleaning, AI monitoring, solar maintenance, drone cleaning, electrostatic cleaning, predictive maintenance, solar efficiency, renewable energy" data-ar="تنظيف الألواح الشمسية، مراقبة الذكاء الاصطناعي، صيانة الطاقة الشمسية، تنظيف بالطائرات، تنظيف كهروستاتيكي، صيانة تنبؤية، كفاءة الطاقة الشمسية، طاقة متجددة" content="solar panel cleaning, AI monitoring, solar maintenance, drone cleaning">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Solar AI Cleaning & Monitoring System">
    <meta property="og:description" content="Revolutionary AI-powered solar panel maintenance with 99% accuracy and 50% cost reduction">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://solar-ai-monitoring.com">
    
    <!-- Fonts - Optimized Loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&family=Tajawal:wght@300;400;500;600;700;800;900&family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Styles -->
    <style>
        :root {
            --primary-blue: #1e40af;
            --secondary-green: #059669;
            --accent-orange: #ea580c;
            --gold: #f59e0b;
            --dark-blue: #1e3a8a;
            --light-blue: #dbeafe;
            --success-green: #10b981;
            --warning-orange: #f97316;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --bg-light: #f8fafc;
            --white: #ffffff;
        }
        
        /* Enhanced Typography */
        .font-primary { font-family: 'Inter', 'Tajawal', sans-serif; }
        .font-heading { font-family: 'Poppins', 'Cairo', sans-serif; }
        .font-arabic { font-family: 'Tajawal', 'Cairo', 'Inter', sans-serif; }
        
        /* Improved Contrast and Readability */
        body {
            font-family: 'Inter', 'Tajawal', sans-serif;
            line-height: 1.7;
            color: var(--text-dark);
            font-size: 16px;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Poppins', 'Cairo', sans-serif;
            font-weight: 700;
            color: var(--text-dark);
            line-height: 1.3;
        }
        
        h1 { font-size: 3.5rem; }
        h2 { font-size: 2.75rem; }
        h3 { font-size: 2rem; }
        h4 { font-size: 1.5rem; }
        
        p, li {
            font-size: 1.125rem;
            line-height: 1.8;
            color: var(--text-light);
        }
        
        /* Enhanced Gradients */
        .gradient-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, #3b82f6 100%);
        }
        
        .gradient-secondary {
            background: linear-gradient(135deg, var(--secondary-green) 0%, var(--success-green) 100%);
        }
        
        .gradient-accent {
            background: linear-gradient(135deg, var(--accent-orange) 0%, var(--warning-orange) 100%);
        }
        
        .gradient-hero {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
        }
        
        /* Enhanced Spacing */
        .section-padding { padding: 6rem 0; }
        .container-spacing { padding: 0 2rem; }
        
        @media (max-width: 768px) {
            .section-padding { padding: 4rem 0; }
            .container-spacing { padding: 0 1rem; }
            h1 { font-size: 2.5rem; }
            h2 { font-size: 2rem; }
            h3 { font-size: 1.5rem; }

            /* Mobile navigation adjustments */
            .btn-nav {
                padding: 0.4rem 0.75rem;
                font-size: 0.8rem;
            }

            /* Arabic mobile adjustments */
            .lang-content[data-lang="ar"] h1 {
                font-size: 2.25rem !important;
                line-height: 1.3;
                margin-bottom: 1.5rem;
            }

            .lang-content[data-lang="ar"] h2 {
                font-size: 1.875rem !important;
                line-height: 1.4;
                margin-bottom: 1.25rem;
            }

            .lang-content[data-lang="ar"] h3 {
                font-size: 1.25rem !important;
                line-height: 1.5;
                margin-bottom: 0.875rem;
            }

            .lang-content[data-lang="ar"] .text-xl {
                font-size: 1rem !important;
                line-height: 1.7;
            }

            .lang-content[data-lang="ar"] .card-enhanced {
                padding: 1.25rem;
                margin-bottom: 1.25rem;
            }

            .lang-content[data-lang="ar"] .gap-8 {
                gap: 1.25rem !important;
            }

            .lang-content[data-lang="ar"] .grid-cols-3 {
                grid-template-columns: 1fr !important;
                gap: 1rem !important;
            }
        }

        @media (max-width: 1024px) {
            /* Tablet navigation adjustments */
            .btn-nav {
                padding: 0.45rem 0.875rem;
                font-size: 0.825rem;
            }

            /* Arabic tablet adjustments */
            .lang-content[data-lang="ar"] h1 {
                font-size: 3rem !important;
                line-height: 1.25;
            }

            .lang-content[data-lang="ar"] h2 {
                font-size: 2.25rem !important;
                line-height: 1.3;
            }

            .lang-content[data-lang="ar"] .card-enhanced {
                padding: 1.375rem;
            }

            .lang-content[data-lang="ar"] .gap-8 {
                gap: 1.375rem !important;
            }
        }

        @media (max-width: 1200px) {
            /* Large tablet/small desktop adjustments */
            .hidden.lg\\:flex.items-center.space-x-6 > .lang-content {
                gap: 1rem;
            }

            .hidden.lg\\:flex.items-center.space-x-6 > .lang-content a {
                font-size: 0.95rem;
            }

            /* Arabic large tablet adjustments */
            .lang-content[data-lang="ar"] h1 {
                font-size: 3.25rem !important;
            }

            .lang-content[data-lang="ar"] h2 {
                font-size: 2.375rem !important;
            }
        }
        
        /* Enhanced Cards */
        .card-enhanced {
            background: var(--white);
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }
        
        .card-enhanced:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
            border-color: var(--primary-blue);
        }
        
        /* Language Switcher Enhanced */
        .language-switcher {
            position: relative;
            z-index: 1000;
        }
        
        .language-btn {
            background: var(--white);
            color: var(--primary-blue);
            border: 2px solid var(--primary-blue);
            padding: 0.75rem 1.25rem;
            border-radius: 0.75rem;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .language-btn:hover {
            background: var(--primary-blue);
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(30, 64, 175, 0.3);
        }
        
        .language-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--white);
            border-radius: 0.75rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border: 1px solid #e5e7eb;
            min-width: 180px;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 0.5rem;
        }
        
        .language-dropdown.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .language-option {
            padding: 1rem 1.25rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: var(--text-dark);
            font-weight: 500;
            transition: background-color 0.2s ease;
            font-size: 1rem;
        }
        
        .language-option:hover {
            background-color: #f3f4f6;
        }
        
        .language-option.active {
            background-color: var(--light-blue);
            color: var(--primary-blue);
            font-weight: 600;
        }
        
        .language-option:first-child {
            border-radius: 0.75rem 0.75rem 0 0;
        }
        
        .language-option:last-child {
            border-radius: 0 0 0.75rem 0.75rem;
        }
        
        /* Professional RTL Support */
        .rtl {
            direction: rtl !important;
            text-align: right !important;
        }

        .rtl .language-dropdown {
            right: auto;
            left: 0;
        }

        .rtl h1, .rtl h2, .rtl h3, .rtl h4, .rtl h5, .rtl h6 {
            font-family: 'Cairo', sans-serif !important;
            text-align: right !important;
            font-weight: 700;
        }

        .rtl p, .rtl li, .rtl span, .rtl div {
            font-family: 'Tajawal', sans-serif !important;
            text-align: right !important;
            line-height: 1.8;
        }

        /* Enhanced Logo RTL Support */
        .rtl .logo-container {
            flex-direction: row-reverse;
        }

        .rtl .logo-container .logo-text {
            margin-left: 0;
            margin-right: 0.75rem;
            text-align: right;
        }

        .rtl .navbar-brand {
            flex-direction: row-reverse;
        }

        /* Enhanced Arabic Layout Spacing */
        .lang-content[data-lang="ar"] .container-spacing {
            padding: 0 2rem;
        }

        .lang-content[data-lang="ar"] .section-padding {
            padding: 6rem 0;
        }

        .lang-content[data-lang="ar"] .mb-8 {
            margin-bottom: 1.5rem !important;
        }

        .lang-content[data-lang="ar"] .mb-6 {
            margin-bottom: 1.25rem !important;
        }

        .lang-content[data-lang="ar"] .mb-4 {
            margin-bottom: 1rem !important;
        }

        .lang-content[data-lang="ar"] .gap-8 {
            gap: 1.5rem !important;
        }

        .lang-content[data-lang="ar"] .gap-6 {
            gap: 1.25rem !important;
        }

        /* RTL Navigation Adjustments */
        .rtl .flex.items-center.space-x-6 {
            flex-direction: row-reverse;
            gap: 1.25rem;
        }

        .rtl .flex.items-center.space-x-6 > * {
            margin-left: 0;
            margin-right: 1.25rem;
        }

        /* Enhanced Arabic Navigation */
        .rtl .desktop-nav {
            direction: rtl;
            gap: 2rem;
        }

        .rtl .desktop-nav a {
            font-family: 'Tajawal', sans-serif;
            font-size: 1rem;
            font-weight: 500;
        }

        .rtl .mobile-nav {
            direction: rtl;
            text-align: right;
        }

        .rtl .mobile-nav a {
            text-align: right;
            padding-right: 1rem;
        }

        .rtl .flex.items-center.space-x-6 > *:last-child {
            margin-right: 0;
        }

        /* RTL Button Adjustments */
        .rtl .btn-nav,
        .rtl .btn-primary,
        .rtl .btn-secondary {
            text-align: center;
            font-family: 'Tajawal', sans-serif !important;
            font-weight: 600;
        }

        /* Arabic Card Styling */
        .lang-content[data-lang="ar"] .card-enhanced {
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .lang-content[data-lang="ar"] .stat-card {
            padding: 1.25rem;
            text-align: center;
        }

        .lang-content[data-lang="ar"] .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .lang-content[data-lang="ar"] .stat-label {
            font-size: 0.875rem;
            line-height: 1.4;
        }

        /* RTL Grid and Flex Adjustments */
        .rtl .grid {
            direction: rtl;
        }

        .rtl .flex {
            direction: rtl;
        }

        .rtl .justify-between {
            flex-direction: row-reverse;
        }

        /* RTL Form Adjustments */
        .rtl .form-input {
            text-align: right;
            direction: rtl;
            font-family: 'Tajawal', sans-serif;
        }

        .rtl .space-y-6 > * {
            text-align: right;
        }

        /* Arabic Dashboard Styling */
        .lang-content[data-lang="ar"] .space-y-6 > div {
            padding: 1rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .lang-content[data-lang="ar"] .justify-between {
            display: flex;
            justify-content: space-between;
            align-items: center;
            direction: rtl;
        }

        /* Enhanced Arabic Typography */
        .lang-content[data-lang="ar"] h1 {
            font-family: 'Cairo', sans-serif !important;
            font-weight: 700 !important;
            line-height: 1.3 !important;
            text-align: right !important;
            letter-spacing: -0.02em;
        }

        .lang-content[data-lang="ar"] h1 span {
            display: block;
            transition: all 0.3s ease;
        }

        .lang-content[data-lang="ar"] h1 .bg-gradient-to-r {
            background: linear-gradient(135deg, #60a5fa, #4ade80) !important;
            -webkit-background-clip: text !important;
            background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            text-shadow: 0 0 30px rgba(96, 165, 250, 0.3);
        }

        .lang-content[data-lang="ar"] h2 {
            font-family: 'Cairo', sans-serif !important;
            font-weight: 700 !important;
            text-align: center !important;
        }

        .lang-content[data-lang="ar"] h3 {
            font-family: 'Cairo', sans-serif !important;
            font-weight: 600 !important;
        }

        .lang-content[data-lang="ar"] p {
            font-family: 'Tajawal', sans-serif !important;
            line-height: 1.8 !important;
            text-align: right !important;
        }

        /* Arabic Hero Section Enhancements */
        .lang-content[data-lang="ar"] .hero-title {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Arabic Card Improvements */
        .lang-content[data-lang="ar"] .card-enhanced {
            direction: rtl;
            text-align: right;
        }

        .lang-content[data-lang="ar"] .card-enhanced h3 {
            text-align: center;
        }

        .lang-content[data-lang="ar"] .card-enhanced p {
            text-align: center;
        }
        
        /* Enhanced Buttons */
        .btn-primary {
            background: var(--primary-blue);
            color: var(--white);
            padding: 1rem 2rem;
            border-radius: 0.75rem;
            font-weight: 600;
            font-size: 1.125rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary:hover {
            background: var(--dark-blue);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
        }

        /* Compact Navigation Button */
        .btn-nav {
            background: var(--primary-blue);
            color: var(--white);
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            white-space: nowrap;
            min-width: fit-content;
        }

        .btn-nav:hover {
            background: var(--dark-blue);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.25);
        }
        
        .btn-secondary {
            background: transparent;
            color: var(--primary-blue);
            border: 2px solid var(--primary-blue);
            padding: 1rem 2rem;
            border-radius: 0.75rem;
            font-weight: 600;
            font-size: 1.125rem;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-secondary:hover {
            background: var(--primary-blue);
            color: var(--white);
            transform: translateY(-2px);
        }
        
        /* Enhanced Stats */
        .stat-card {
            background: var(--white);
            padding: 2rem;
            border-radius: 1rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
            border-color: var(--primary-blue);
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            font-family: 'Poppins', sans-serif;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-light);
        }
        
        /* Enhanced Animations */
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .animate-fade-in {
            animation: fadeIn 0.8s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Professional Language Content Management */
        .lang-content {
            display: none !important;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
        }

        .lang-content.active {
            display: block !important;
            opacity: 1;
            transform: translateY(0);
        }

        .lang-content.active.flex {
            display: flex !important;
            opacity: 1;
            transform: translateY(0);
        }

        .lang-content.active.grid {
            display: grid !important;
            opacity: 1;
            transform: translateY(0);
        }

        /* Initial English content visibility */
        .lang-content[data-lang="en"] {
            display: block !important;
            opacity: 1;
            transform: translateY(0);
        }

        .lang-content[data-lang="en"].flex {
            display: flex !important;
            opacity: 1;
            transform: translateY(0);
        }

        .lang-content[data-lang="en"].grid {
            display: grid !important;
            opacity: 1;
            transform: translateY(0);
        }

        /* Professional Arabic Typography and Layout */
        .lang-content[data-lang="ar"] {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            text-align: right;
            direction: rtl;
            line-height: 1.7;
        }

        .lang-content[data-lang="ar"] h1 {
            font-family: 'Cairo', sans-serif;
            font-weight: 700;
            text-align: right;
            font-size: 3.5rem;
            line-height: 1.2;
            margin-bottom: 2rem;
        }

        .lang-content[data-lang="ar"] h2 {
            font-family: 'Cairo', sans-serif;
            font-weight: 700;
            text-align: right;
            font-size: 2.5rem;
            line-height: 1.3;
            margin-bottom: 1.5rem;
        }

        .lang-content[data-lang="ar"] h3 {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            text-align: right;
            font-size: 1.5rem;
            line-height: 1.4;
            margin-bottom: 1rem;
        }

        .lang-content[data-lang="ar"] h4 {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            text-align: right;
            font-size: 1.25rem;
            line-height: 1.4;
            margin-bottom: 0.75rem;
        }

        .lang-content[data-lang="ar"] p {
            font-family: 'Tajawal', sans-serif;
            text-align: right;
            line-height: 1.8;
            font-size: 1rem;
            margin-bottom: 1rem;
        }

        .lang-content[data-lang="ar"] span,
        .lang-content[data-lang="ar"] div {
            font-family: 'Tajawal', sans-serif;
            text-align: right;
            line-height: 1.7;
        }

        /* Arabic text sizing adjustments */
        .lang-content[data-lang="ar"] .text-xl {
            font-size: 1.125rem !important;
            line-height: 1.8;
        }

        .lang-content[data-lang="ar"] .text-lg {
            font-size: 1rem !important;
            line-height: 1.7;
        }

        .lang-content[data-lang="ar"] .text-sm {
            font-size: 0.875rem !important;
            line-height: 1.6;
        }

        /* English-specific styling for professional appearance */
        .lang-content[data-lang="en"] {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            text-align: left;
            direction: ltr;
        }

        .lang-content[data-lang="en"] h1,
        .lang-content[data-lang="en"] h2,
        .lang-content[data-lang="en"] h3,
        .lang-content[data-lang="en"] h4 {
            font-family: 'Inter', sans-serif;
            font-weight: 700;
            text-align: left;
        }
        
        /* Enhanced Form Styles */
        .form-input {
            width: 100%;
            padding: 1rem 1.25rem;
            border-radius: 0.75rem;
            border: 2px solid #e5e7eb;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--white);
            color: var(--text-dark);
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
        }
        
        .form-input::placeholder {
            color: #9ca3af;
        }

        /* Professional Page Transitions */
        body {
            transition: all 0.3s ease;
        }

        /* Language-specific font loading */
        .font-arabic {
            font-family: 'Cairo', 'Tajawal', sans-serif !important;
        }

        .font-arabic * {
            font-family: 'Tajawal', 'Cairo', sans-serif !important;
        }

        .font-arabic h1, .font-arabic h2, .font-arabic h3, .font-arabic h4 {
            font-family: 'Cairo', 'Tajawal', sans-serif !important;
            font-weight: 700;
        }

        /* Smooth content transitions */
        .lang-content {
            will-change: opacity, transform;
        }

        /* Professional spacing adjustments */
        .rtl .space-x-6 > * + * {
            margin-left: 0 !important;
            margin-right: 1.5rem !important;
        }

        .rtl .space-x-8 > * + * {
            margin-left: 0 !important;
            margin-right: 2rem !important;
        }

        /* Better button alignment for RTL */
        .rtl .btn-nav,
        .rtl .btn-primary {
            direction: ltr;
            text-align: center;
        }

        /* Success Message */
        .success-message {
            background: #dcfce7;
            color: #166534;
            padding: 1rem 1.5rem;
            border-radius: 0.75rem;
            border: 1px solid #bbf7d0;
            margin-top: 1rem;
            display: none;
        }
        
        .success-message.show {
            display: block;
            animation: slideDown 0.3s ease;
        }
        
        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Loading State */
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }
        
        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>

<body class="font-primary bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full z-50 transition-all duration-300" id="navbar">
        <div class="max-w-7xl mx-auto container-spacing">
            <div class="flex justify-between items-center h-20">
                <!-- Logo -->
                <div class="flex items-center logo-container">
                    <div class="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center">
                        <span class="text-white font-bold text-2xl">☀️</span>
                    </div>
                    <!-- English Logo Text -->
                    <div class="lang-content active" data-lang="en">
                        <span class="ml-3 text-2xl font-bold text-gray-900 font-heading logo-text">Solar AI</span>
                    </div>
                    <!-- Arabic Logo Text -->
                    <div class="lang-content font-arabic" data-lang="ar">
                        <span class="mr-3 text-2xl font-bold text-gray-900 logo-text" style="font-family: 'Cairo', sans-serif; font-weight: 700;">Solar AI</span>
                    </div>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden lg:flex items-center space-x-6">
                    <!-- English Navigation -->
                    <div class="lang-content active flex items-center space-x-6" data-lang="en">
                        <a href="#features" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">Features</a>
                        <a href="#technology" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">Technology</a>
                        <a href="#case-studies" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">Case Studies</a>
                        <a href="#contact" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">Contact</a>
                        <button class="btn-nav" onclick="scrollToSection('contact')">
                            Start Free Trial
                        </button>
                    </div>

                    <!-- Arabic Navigation -->
                    <div class="lang-content font-arabic flex items-center desktop-nav" data-lang="ar" style="direction: rtl; gap: 2rem; font-family: 'Tajawal', sans-serif;">
                        <button class="btn-nav" onclick="scrollToSection('contact')" style="font-family: 'Tajawal', sans-serif; font-weight: 600; padding: 0.75rem 1.5rem; border-radius: 0.5rem; background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; border: none; cursor: pointer; transition: all 0.3s ease;">
                            تجربة مجانية
                        </button>
                        <a href="#contact" class="text-gray-700 hover:text-blue-600 font-medium transition-colors" style="font-family: 'Tajawal', sans-serif; font-size: 1rem; font-weight: 500;">تواصل معنا</a>
                        <a href="#case-studies" class="text-gray-700 hover:text-blue-600 font-medium transition-colors" style="font-family: 'Tajawal', sans-serif; font-size: 1rem; font-weight: 500;">دراسات الحالة</a>
                        <a href="#technology" class="text-gray-700 hover:text-blue-600 font-medium transition-colors" style="font-family: 'Tajawal', sans-serif; font-size: 1rem; font-weight: 500;">التقنية</a>
                        <a href="#features" class="text-gray-700 hover:text-blue-600 font-medium transition-colors" style="font-family: 'Tajawal', sans-serif; font-size: 1rem; font-weight: 500;">الميزات</a>
                    </div>
                    
                    <!-- Language Switcher -->
                    <div class="language-switcher">
                        <button class="language-btn" onclick="toggleLanguageDropdown()">
                            <span id="current-flag">🇺🇸</span>
                            <span id="current-lang">EN</span>
                            <span class="transition-transform duration-200" id="dropdown-arrow">▼</span>
                        </button>
                        <div class="language-dropdown" id="language-dropdown">
                            <div class="language-option active" onclick="switchLanguage('en')">
                                <span>🇺🇸</span>
                                <span>English</span>
                            </div>
                            <div class="language-option" onclick="switchLanguage('ar')">
                                <span>🇸🇦</span>
                                <span>العربية</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Mobile Menu Button -->
                <div class="lg:hidden">
                    <button class="text-gray-700 hover:text-blue-600 focus:outline-none" onclick="toggleMobileMenu()">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- Mobile Menu -->
            <div class="lg:hidden hidden" id="mobile-menu">
                <div class="px-2 pt-2 pb-3 space-y-1 bg-white border-t">
                    <!-- English Mobile Menu -->
                    <div class="lang-content active" data-lang="en">
                        <a href="#features" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Features</a>
                        <a href="#technology" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Technology</a>
                        <a href="#case-studies" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Case Studies</a>
                        <a href="#contact" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Contact</a>
                        <button class="btn-primary w-full mt-4" onclick="scrollToSection('contact')">Start Free Trial</button>
                    </div>

                    <!-- Arabic Mobile Menu -->
                    <div class="lang-content font-arabic mobile-nav" data-lang="ar" style="direction: rtl; text-align: right;">
                        <a href="#features" class="block px-3 py-2 text-gray-700 hover:text-blue-600" style="font-family: 'Tajawal', sans-serif; text-align: right; padding-right: 1rem; font-weight: 500;">الميزات</a>
                        <a href="#technology" class="block px-3 py-2 text-gray-700 hover:text-blue-600" style="font-family: 'Tajawal', sans-serif; text-align: right; padding-right: 1rem; font-weight: 500;">التقنية</a>
                        <a href="#case-studies" class="block px-3 py-2 text-gray-700 hover:text-blue-600" style="font-family: 'Tajawal', sans-serif; text-align: right; padding-right: 1rem; font-weight: 500;">دراسات الحالة</a>
                        <a href="#contact" class="block px-3 py-2 text-gray-700 hover:text-blue-600" style="font-family: 'Tajawal', sans-serif; text-align: right; padding-right: 1rem; font-weight: 500;">تواصل معنا</a>
                        <button class="btn-primary w-full mt-4" onclick="scrollToSection('contact')" style="font-family: 'Tajawal', sans-serif; font-weight: 600; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border: none; padding: 1rem; border-radius: 0.5rem; color: white; cursor: pointer;">تجربة مجانية</button>
                    </div>
                    
                    <!-- Mobile Language Switcher -->
                    <div class="pt-4 border-t">
                        <div class="flex justify-center space-x-4">
                            <button onclick="switchLanguage('en')" class="flex items-center space-x-2 px-4 py-2 rounded-lg border">
                                <span>🇺🇸</span>
                                <span>English</span>
                            </button>
                            <button onclick="switchLanguage('ar')" class="flex items-center space-x-2 px-4 py-2 rounded-lg border">
                                <span>🇸🇦</span>
                                <span>العربية</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="gradient-hero text-white pt-24 pb-16 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-blue-900/50 to-transparent"></div>
        <div class="max-w-7xl mx-auto container-spacing relative z-10">
            <div class="grid lg:grid-cols-2 gap-16 items-center">
                <div class="animate-fade-in">
                    <!-- English Hero Content -->
                    <div class="lang-content active" data-lang="en">
                        <h1 class="font-heading leading-tight mb-8">
                            Revolutionary
                            <span class="bg-gradient-to-r from-blue-400 to-green-400 bg-clip-text text-transparent">
                                AI-Powered
                            </span>
                            Solar Panel Maintenance
                        </h1>
                        <p class="text-xl text-gray-300 mb-10 leading-relaxed">
                            Transform your solar operations with cutting-edge artificial intelligence. Achieve 99% detection accuracy,
                            reduce costs by 50%, and save 85% water consumption. Experience the future of solar panel maintenance today.
                        </p>
                    </div>

                    <!-- Arabic Hero Content -->
                    <div class="lang-content font-arabic" data-lang="ar" style="direction: rtl; text-align: right; padding: 0 1rem;">
                        <h1 class="font-heading leading-tight mb-8 hero-title" style="font-family: 'Cairo', sans-serif; font-size: 3.5rem; line-height: 1.2; text-align: right; font-weight: 700; margin-bottom: 2rem; color: white;">
                            <span style="display: block; margin-bottom: 0.5rem; font-size: 3.25rem;">صيانة ثورية للألواح الشمسية</span>
                            <span style="display: block; margin: 0.5rem 0; font-size: 2.75rem; color: #e5e7eb;">مدعومة بتقنيات</span>
                            <span class="bg-gradient-to-r from-blue-400 to-green-400 bg-clip-text text-transparent" style="display: block; margin: 0.5rem 0; font-size: 4rem; font-weight: 800; text-shadow: 0 0 30px rgba(96, 165, 250, 0.3);">
                                الذكاء الاصطناعي
                            </span>
                        </h1>
                        <p class="text-xl text-gray-300 mb-10 leading-relaxed" style="font-family: 'Tajawal', sans-serif; font-size: 1.25rem; line-height: 1.8; text-align: right; color: #d1d5db; margin-bottom: 2.5rem; max-width: 100%; padding: 0 0.5rem;">
                            حوّل عمليات الطاقة الشمسية لديك بأحدث تقنيات الذكاء الاصطناعي. احصل على دقة كشف 99%،
                            وقلل التكاليف بنسبة 50%، ووفر 85% من استهلاك المياه. اختبر مستقبل صيانة الألواح الشمسية اليوم.
                        </p>
                    </div>

                    <!-- Key Stats - English -->
                    <div class="lang-content active grid grid-cols-3 gap-8 mb-12" data-lang="en">
                        <div class="stat-card bg-white/10 backdrop-blur-lg border-white/20">
                            <div class="stat-number text-blue-400">99%</div>
                            <div class="stat-label text-white">AI Detection Accuracy</div>
                        </div>
                        <div class="stat-card bg-white/10 backdrop-blur-lg border-white/20">
                            <div class="stat-number text-green-400">50%</div>
                            <div class="stat-label text-white">Cost Reduction</div>
                        </div>
                        <div class="stat-card bg-white/10 backdrop-blur-lg border-white/20">
                            <div class="stat-number text-orange-400">85%</div>
                            <div class="stat-label text-white">Water Savings</div>
                        </div>
                    </div>

                    <!-- Key Stats - Arabic -->
                    <div class="lang-content font-arabic grid grid-cols-3 gap-6 mb-12" data-lang="ar" style="direction: rtl; padding: 0 1rem;">
                        <div class="stat-card bg-white/10 backdrop-blur-lg border-white/20" style="padding: 1.5rem; text-align: center; border-radius: 1rem; border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(10px);">
                            <div class="stat-number text-blue-400" style="font-size: 2.25rem; font-weight: 700; margin-bottom: 0.75rem; color: #60a5fa;">99%</div>
                            <div class="stat-label text-white" style="font-size: 0.875rem; line-height: 1.4; font-family: 'Tajawal', sans-serif; color: white; font-weight: 500;">دقة الكشف بالذكاء الاصطناعي</div>
                        </div>
                        <div class="stat-card bg-white/10 backdrop-blur-lg border-white/20" style="padding: 1.5rem; text-align: center; border-radius: 1rem; border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(10px);">
                            <div class="stat-number text-green-400" style="font-size: 2.25rem; font-weight: 700; margin-bottom: 0.75rem; color: #4ade80;">50%</div>
                            <div class="stat-label text-white" style="font-size: 0.875rem; line-height: 1.4; font-family: 'Tajawal', sans-serif; color: white; font-weight: 500;">تقليل التكاليف</div>
                        </div>
                        <div class="stat-card bg-white/10 backdrop-blur-lg border-white/20" style="padding: 1.5rem; text-align: center; border-radius: 1rem; border: 1px solid rgba(255, 255, 255, 0.2); backdrop-filter: blur(10px);">
                            <div class="stat-number text-orange-400" style="font-size: 2.25rem; font-weight: 700; margin-bottom: 0.75rem; color: #fb923c;">85%</div>
                            <div class="stat-label text-white" style="font-size: 0.875rem; line-height: 1.4; font-family: 'Tajawal', sans-serif; color: white; font-weight: 500;">توفير المياه</div>
                        </div>
                    </div>

                    <!-- CTA Buttons - English -->
                    <div class="lang-content active flex flex-col sm:flex-row gap-6" data-lang="en">
                        <button class="btn-primary text-lg px-8 py-4" onclick="scrollToSection('contact')">
                            🚀 Start Free Trial
                        </button>
                    </div>

                    <!-- CTA Buttons - Arabic -->
                    <div class="lang-content font-arabic flex flex-col sm:flex-row gap-6" data-lang="ar" style="direction: rtl; padding: 0 1rem;">
                        <button class="btn-primary text-lg px-8 py-4" onclick="scrollToSection('contact')" style="font-family: 'Tajawal', sans-serif; font-weight: 600; font-size: 1.125rem; padding: 1rem 2rem; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border: none; border-radius: 0.75rem; color: white; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);">
                            🚀 ابدأ التجربة المجانية
                        </button>
                    </div>
                </div>

                <!-- Live Dashboard Widget -->
                <div class="relative animate-float">
                    <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
                        <!-- English Dashboard -->
                        <div class="lang-content active" data-lang="en">
                            <h3 class="text-2xl font-bold mb-8 text-center">🔴 Live System Status</h3>
                            <div class="space-y-6">
                                <div class="flex justify-between items-center">
                                    <span class="text-lg">Panels Monitored</span>
                                    <span class="font-bold text-green-400 text-xl">1,247</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-lg">Efficiency Improvement</span>
                                    <span class="font-bold text-blue-400 text-xl">+25%</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-lg">Water Saved Today</span>
                                    <span class="font-bold text-orange-400 text-xl">2,840L</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-lg">Cost Savings This Month</span>
                                    <span class="font-bold text-green-400 text-xl">$47,250</span>
                                </div>
                            </div>
                        </div>

                        <!-- Arabic Dashboard -->
                        <div class="lang-content font-arabic" data-lang="ar" style="direction: rtl; text-align: right; padding: 0 1rem;">
                            <h3 class="text-2xl font-bold mb-8 text-center" style="font-family: 'Cairo', sans-serif; font-size: 1.875rem; font-weight: 700; text-align: center; margin-bottom: 2rem; color: white;">🔴 حالة النظام المباشرة</h3>
                            <div class="space-y-6" style="margin-bottom: 1.5rem;">
                                <div class="flex justify-between items-center" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 1rem 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                                    <span class="text-lg" style="font-family: 'Tajawal', sans-serif; font-size: 1.125rem; color: white; font-weight: 500;">الألواح المراقبة</span>
                                    <span class="font-bold text-green-400 text-xl" style="font-weight: 700; color: #4ade80; font-size: 1.25rem;">1,247</span>
                                </div>
                                <div class="flex justify-between items-center" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 1rem 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                                    <span class="text-lg" style="font-family: 'Tajawal', sans-serif; font-size: 1.125rem; color: white; font-weight: 500;">تحسن الكفاءة</span>
                                    <span class="font-bold text-blue-400 text-xl" style="font-weight: 700; color: #60a5fa; font-size: 1.25rem;">+25%</span>
                                </div>
                                <div class="flex justify-between items-center" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 1rem 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                                    <span class="text-lg" style="font-family: 'Tajawal', sans-serif; font-size: 1.125rem; color: white; font-weight: 500;">المياه الموفرة اليوم</span>
                                    <span class="font-bold text-orange-400 text-xl" style="font-weight: 700; color: #fb923c; font-size: 1.25rem;">2,840 لتر</span>
                                </div>
                                <div class="flex justify-between items-center" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 1rem 0;">
                                    <span class="text-lg" style="font-family: 'Tajawal', sans-serif; font-size: 1.125rem; color: white; font-weight: 500;">التوفير هذا الشهر</span>
                                    <span class="font-bold text-green-400 text-xl" style="font-weight: 700; color: #4ade80; font-size: 1.25rem;">177,188 ريال</span>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8 h-3 bg-gray-700 rounded-full overflow-hidden">
                            <div class="h-full gradient-secondary rounded-full animate-pulse" style="width: 94%"></div>
                        </div>
                        <p class="text-sm text-gray-400 mt-3 text-center">System Performance: 94% Optimal</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="section-padding bg-white">
        <div class="max-w-7xl mx-auto container-spacing">
            <!-- English Features Header -->
            <div class="lang-content active text-center mb-20" data-lang="en">
                <h2 class="font-heading mb-6">9 Revolutionary Cleaning Technologies</h2>
                <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                    From traditional water-based cleaning to cutting-edge electrostatic and UV technologies.
                    Each method is optimized for specific conditions to maximize efficiency and minimize environmental impact.
                </p>
            </div>

            <!-- Arabic Features Header -->
            <div class="lang-content font-arabic text-center mb-20" data-lang="ar" style="direction: rtl;">
                <h2 class="font-heading mb-6" style="font-size: 2.75rem; font-weight: 700; text-align: center; margin-bottom: 2rem; font-family: 'Cairo', sans-serif; color: #1f2937; letter-spacing: -0.02em;">
                    <span style="display: block; margin-bottom: 0.5rem;">9 تقنيات تنظيف</span>
                    <span style="color: #3b82f6;">ثورية ومتطورة</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed" style="font-size: 1.25rem; line-height: 1.8; text-align: center; max-width: 56rem; margin: 0 auto; font-family: 'Tajawal', sans-serif; color: #6b7280;">
                    من التنظيف التقليدي بالمياه إلى التقنيات المتطورة الكهروستاتيكية والأشعة فوق البنفسجية.
                    كل طريقة محسّنة لظروف محددة لتحقيق أقصى كفاءة وأقل تأثير بيئي.
                </p>
            </div>

            <div class="grid lg:grid-cols-3 md:grid-cols-2 gap-8">
                <!-- Method 1: Drone Water Cleaning -->
                <div class="card-enhanced">
                    <div class="text-5xl mb-6 text-center">🚁</div>
                    <!-- English Content -->
                    <div class="lang-content active" data-lang="en">
                        <h3 class="font-heading mb-4 text-center">Precision Drone Water Cleaning</h3>
                        <p class="text-gray-600 mb-6 text-center">
                            Advanced drone technology with precision water targeting for thorough panel cleaning.
                        </p>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="font-semibold">Efficiency:</span>
                                <span class="text-blue-600 font-bold">85%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-semibold">Water Usage:</span>
                                <span class="text-orange-600 font-bold">0.7 L/m²</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-semibold">Cost per MW:</span>
                                <span class="text-green-600 font-bold">$560</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                            <p class="text-sm text-blue-800 font-semibold">✓ Best for: Regular maintenance in water-abundant areas</p>
                        </div>
                    </div>

                    <!-- Arabic Content -->
                    <div class="lang-content font-arabic" data-lang="ar" style="direction: rtl; text-align: right;">
                        <h3 class="font-heading mb-4 text-center" style="font-size: 1.5rem; font-weight: 600; text-align: center; margin-bottom: 1rem; font-family: 'Cairo', sans-serif;">التنظيف المائي الدقيق بالطائرات</h3>
                        <p class="text-gray-600 mb-6 text-center" style="font-size: 1rem; line-height: 1.7; text-align: center; margin-bottom: 1.5rem; font-family: 'Tajawal', sans-serif;">
                            تقنية طائرات متطورة مع استهداف مائي دقيق لتنظيف شامل للألواح.
                        </p>
                        <div class="space-y-3" style="margin-bottom: 1.5rem;">
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">الكفاءة:</span>
                                <span class="text-blue-600 font-bold" style="font-weight: 700; color: #2563eb;">85%</span>
                            </div>
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">استهلاك المياه:</span>
                                <span class="text-orange-600 font-bold" style="font-weight: 700; color: #ea580c;">0.7 لتر/م²</span>
                            </div>
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">التكلفة/ميجاوات:</span>
                                <span class="text-green-600 font-bold" style="font-weight: 700; color: #16a34a;">2,100 ريال</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-blue-50 rounded-lg" style="margin-top: 1.5rem; padding: 1rem; background-color: #eff6ff; border-radius: 0.5rem;">
                            <p class="text-sm text-blue-800 font-semibold" style="font-size: 0.875rem; color: #1e40af; font-weight: 600; text-align: right; font-family: 'Tajawal', sans-serif;">✓ الأفضل لـ: الصيانة المنتظمة في المناطق الغنية بالمياه</p>
                        </div>
                    </div>
                </div>

                <!-- Method 2: Waterless Drone Technology -->
                <div class="card-enhanced">
                    <div class="text-5xl mb-6 text-center">⚡</div>
                    <!-- English Content -->
                    <div class="lang-content active" data-lang="en">
                        <h3 class="font-heading mb-4 text-center">Advanced Waterless Drones</h3>
                        <p class="text-gray-600 mb-6 text-center">
                            Revolutionary waterless cleaning technology perfect for water-scarce desert environments.
                        </p>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="font-semibold">Efficiency:</span>
                                <span class="text-blue-600 font-bold">90%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-semibold">Water Usage:</span>
                                <span class="text-green-600 font-bold">0.0 L/m²</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-semibold">Cost per MW:</span>
                                <span class="text-green-600 font-bold">$450</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-green-50 rounded-lg">
                            <p class="text-sm text-green-800 font-semibold">✓ Best for: Desert installations, water conservation</p>
                        </div>
                    </div>

                    <!-- Arabic Content -->
                    <div class="lang-content font-arabic" data-lang="ar" style="direction: rtl; text-align: right;">
                        <h3 class="font-heading mb-4 text-center" style="font-size: 1.5rem; font-weight: 600; text-align: center; margin-bottom: 1rem; font-family: 'Cairo', sans-serif;">الطائرات المتطورة بدون مياه</h3>
                        <p class="text-gray-600 mb-6 text-center" style="font-size: 1rem; line-height: 1.7; text-align: center; margin-bottom: 1.5rem; font-family: 'Tajawal', sans-serif;">
                            تقنية تنظيف ثورية بدون مياه مثالية للبيئات الصحراوية قليلة المياه.
                        </p>
                        <div class="space-y-3" style="margin-bottom: 1.5rem;">
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">الكفاءة:</span>
                                <span class="text-blue-600 font-bold" style="font-weight: 700; color: #2563eb;">90%</span>
                            </div>
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">استهلاك المياه:</span>
                                <span class="text-green-600 font-bold" style="font-weight: 700; color: #16a34a;">0.0 لتر/م²</span>
                            </div>
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">التكلفة/ميجاوات:</span>
                                <span class="text-green-600 font-bold" style="font-weight: 700; color: #16a34a;">1,688 ريال</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-green-50 rounded-lg" style="margin-top: 1.5rem; padding: 1rem; background-color: #f0fdf4; border-radius: 0.5rem;">
                            <p class="text-sm text-green-800 font-semibold" style="font-size: 0.875rem; color: #166534; font-weight: 600; text-align: right; font-family: 'Tajawal', sans-serif;">✓ الأفضل لـ: التركيبات الصحراوية، توفير المياه</p>
                        </div>
                    </div>
                </div>

                <!-- Method 3: Autonomous Crawler Robots -->
                <div class="card-enhanced">
                    <div class="text-5xl mb-6 text-center">🤖</div>
                    <!-- English Content -->
                    <div class="lang-content active" data-lang="en">
                        <h3 class="font-heading mb-4 text-center">Autonomous Crawler Robots</h3>
                        <p class="text-gray-600 mb-6 text-center">
                            Self-navigating robots that move directly on panel surfaces for precise, thorough cleaning.
                        </p>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="font-semibold">Efficiency:</span>
                                <span class="text-blue-600 font-bold">95%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-semibold">Water Usage:</span>
                                <span class="text-green-600 font-bold">0.1 L/m²</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-semibold">Cost per MW:</span>
                                <span class="text-green-600 font-bold">$300</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-purple-50 rounded-lg">
                            <p class="text-sm text-purple-800 font-semibold">✓ Best for: Maximum efficiency, minimal water use</p>
                        </div>
                    </div>

                    <!-- Arabic Content -->
                    <div class="lang-content font-arabic" data-lang="ar" style="direction: rtl; text-align: right;">
                        <h3 class="font-heading mb-4 text-center" style="font-size: 1.5rem; font-weight: 600; text-align: center; margin-bottom: 1rem; font-family: 'Cairo', sans-serif;">الروبوتات الزاحفة الذاتية</h3>
                        <p class="text-gray-600 mb-6 text-center" style="font-size: 1rem; line-height: 1.7; text-align: center; margin-bottom: 1.5rem; font-family: 'Tajawal', sans-serif;">
                            روبوتات ذاتية التنقل تتحرك مباشرة على أسطح الألواح للتنظيف الدقيق والشامل.
                        </p>
                        <div class="space-y-3" style="margin-bottom: 1.5rem;">
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">الكفاءة:</span>
                                <span class="text-blue-600 font-bold" style="font-weight: 700; color: #2563eb;">95%</span>
                            </div>
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">استهلاك المياه:</span>
                                <span class="text-green-600 font-bold" style="font-weight: 700; color: #16a34a;">0.1 لتر/م²</span>
                            </div>
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">التكلفة/ميجاوات:</span>
                                <span class="text-green-600 font-bold" style="font-weight: 700; color: #16a34a;">1,125 ريال</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-purple-50 rounded-lg" style="margin-top: 1.5rem; padding: 1rem; background-color: #faf5ff; border-radius: 0.5rem;">
                            <p class="text-sm text-purple-800 font-semibold" style="font-size: 0.875rem; color: #6b21a8; font-weight: 600; text-align: right; font-family: 'Tajawal', sans-serif;">✓ الأفضل لـ: أقصى كفاءة، أقل استهلاك للمياه</p>
                        </div>
                    </div>
                </div>

                <!-- Method 4: Electrostatic Cleaning (NEW) -->
                <div class="card-enhanced border-2 border-orange-200 bg-gradient-to-br from-orange-50 to-yellow-50">
                    <div class="flex items-center justify-center mb-4">
                        <span class="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold">NEW</span>
                    </div>
                    <div class="text-5xl mb-6 text-center">⚡</div>
                    <!-- English Content -->
                    <div class="lang-content active" data-lang="en">
                        <h3 class="font-heading mb-4 text-center">Electrostatic Dust Repulsion</h3>
                        <p class="text-gray-600 mb-6 text-center">
                            Revolutionary electrostatic charge technology that repels dust particles before they settle.
                        </p>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="font-semibold">Efficiency:</span>
                                <span class="text-blue-600 font-bold">85%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-semibold">Water Usage:</span>
                                <span class="text-green-600 font-bold">0.0 L/m²</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-semibold">Cost per MW:</span>
                                <span class="text-green-600 font-bold">$340</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-orange-100 rounded-lg">
                            <p class="text-sm text-orange-800 font-semibold">✓ Best for: Preventive cleaning, dusty environments</p>
                        </div>
                    </div>

                    <!-- Arabic Content -->
                    <div class="lang-content font-arabic" data-lang="ar" style="direction: rtl; text-align: right;">
                        <h3 class="font-heading mb-4 text-center" style="font-size: 1.5rem; font-weight: 600; text-align: center; margin-bottom: 1rem; font-family: 'Cairo', sans-serif;">طرد الغبار الكهروستاتيكي</h3>
                        <p class="text-gray-600 mb-6 text-center" style="font-size: 1rem; line-height: 1.7; text-align: center; margin-bottom: 1.5rem; font-family: 'Tajawal', sans-serif;">
                            تقنية الشحن الكهروستاتيكي الثورية التي تطرد جزيئات الغبار قبل استقرارها.
                        </p>
                        <div class="space-y-3" style="margin-bottom: 1.5rem;">
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">الكفاءة:</span>
                                <span class="text-blue-600 font-bold" style="font-weight: 700; color: #2563eb;">85%</span>
                            </div>
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">استهلاك المياه:</span>
                                <span class="text-green-600 font-bold" style="font-weight: 700; color: #16a34a;">0.0 لتر/م²</span>
                            </div>
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">التكلفة/ميجاوات:</span>
                                <span class="text-green-600 font-bold" style="font-weight: 700; color: #16a34a;">1,275 ريال</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-orange-100 rounded-lg" style="margin-top: 1.5rem; padding: 1rem; background-color: #fed7aa; border-radius: 0.5rem;">
                            <p class="text-sm text-orange-800 font-semibold" style="font-size: 0.875rem; color: #9a3412; font-weight: 600; text-align: right; font-family: 'Tajawal', sans-serif;">✓ الأفضل لـ: التنظيف الوقائي، البيئات المغبرة</p>
                        </div>
                    </div>
                </div>

                <!-- Method 5: UV Surface Treatment (NEW) -->
                <div class="card-enhanced border-2 border-purple-200 bg-gradient-to-br from-purple-50 to-pink-50">
                    <div class="flex items-center justify-center mb-4">
                        <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-bold">NEW</span>
                    </div>
                    <div class="text-5xl mb-6 text-center">☀️</div>
                    <!-- English Content -->
                    <div class="lang-content active" data-lang="en">
                        <h3 class="font-heading mb-4 text-center">UV Surface Sterilization</h3>
                        <p class="text-gray-600 mb-6 text-center">
                            Advanced UV light treatment for organic contamination removal and surface sterilization.
                        </p>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="font-semibold">Efficiency:</span>
                                <span class="text-blue-600 font-bold">65%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-semibold">Water Usage:</span>
                                <span class="text-green-600 font-bold">0.0 L/m²</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-semibold">Cost per MW:</span>
                                <span class="text-green-600 font-bold">$280</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-purple-100 rounded-lg">
                            <p class="text-sm text-purple-800 font-semibold">✓ Best for: Organic contamination, bird droppings</p>
                        </div>
                    </div>

                    <!-- Arabic Content -->
                    <div class="lang-content font-arabic" data-lang="ar" style="direction: rtl; text-align: right;">
                        <h3 class="font-heading mb-4 text-center" style="font-size: 1.5rem; font-weight: 600; text-align: center; margin-bottom: 1rem; font-family: 'Cairo', sans-serif;">التعقيم بالأشعة فوق البنفسجية</h3>
                        <p class="text-gray-600 mb-6 text-center" style="font-size: 1rem; line-height: 1.7; text-align: center; margin-bottom: 1.5rem; font-family: 'Tajawal', sans-serif;">
                            معالجة متطورة بالأشعة فوق البنفسجية لإزالة التلوث العضوي وتعقيم الأسطح.
                        </p>
                        <div class="space-y-3" style="margin-bottom: 1.5rem;">
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">الكفاءة:</span>
                                <span class="text-blue-600 font-bold" style="font-weight: 700; color: #2563eb;">65%</span>
                            </div>
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">استهلاك المياه:</span>
                                <span class="text-green-600 font-bold" style="font-weight: 700; color: #16a34a;">0.0 لتر/م²</span>
                            </div>
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">التكلفة/ميجاوات:</span>
                                <span class="text-green-600 font-bold" style="font-weight: 700; color: #16a34a;">1,050 ريال</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-purple-100 rounded-lg" style="margin-top: 1.5rem; padding: 1rem; background-color: #e9d5ff; border-radius: 0.5rem;">
                            <p class="text-sm text-purple-800 font-semibold" style="font-size: 0.875rem; color: #6b21a8; font-weight: 600; text-align: right; font-family: 'Tajawal', sans-serif;">✓ الأفضل لـ: التلوث العضوي، فضلات الطيور</p>
                        </div>
                    </div>
                </div>

                <!-- Method 6: AI Predictive Maintenance (NEW) -->
                <div class="card-enhanced border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
                    <div class="flex items-center justify-center mb-4">
                        <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-bold">AI-POWERED</span>
                    </div>
                    <div class="text-5xl mb-6 text-center">🧠</div>
                    <!-- English Content -->
                    <div class="lang-content active" data-lang="en">
                        <h3 class="font-heading mb-4 text-center">AI Predictive Scheduling</h3>
                        <p class="text-gray-600 mb-6 text-center">
                            Machine learning algorithms optimize cleaning schedules and method selection for maximum efficiency.
                        </p>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="font-semibold">Prediction Accuracy:</span>
                                <span class="text-blue-600 font-bold">95%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-semibold">Efficiency Boost:</span>
                                <span class="text-green-600 font-bold">+30%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-semibold">Cost Optimization:</span>
                                <span class="text-green-600 font-bold">-40%</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-blue-100 rounded-lg">
                            <p class="text-sm text-blue-800 font-semibold">✓ Best for: Optimizing all cleaning methods</p>
                        </div>
                    </div>

                    <!-- Arabic Content -->
                    <div class="lang-content font-arabic" data-lang="ar" style="direction: rtl; text-align: right;">
                        <h3 class="font-heading mb-4 text-center" style="font-size: 1.5rem; font-weight: 600; text-align: center; margin-bottom: 1rem; font-family: 'Cairo', sans-serif;">الجدولة التنبؤية بالذكاء الاصطناعي</h3>
                        <p class="text-gray-600 mb-6 text-center" style="font-size: 1rem; line-height: 1.7; text-align: center; margin-bottom: 1.5rem; font-family: 'Tajawal', sans-serif;">
                            خوارزميات التعلم الآلي تحسن جداول التنظيف واختيار الطرق لأقصى كفاءة.
                        </p>
                        <div class="space-y-3" style="margin-bottom: 1.5rem;">
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">دقة التنبؤ:</span>
                                <span class="text-blue-600 font-bold" style="font-weight: 700; color: #2563eb;">95%</span>
                            </div>
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">تعزيز الكفاءة:</span>
                                <span class="text-green-600 font-bold" style="font-weight: 700; color: #16a34a;">+30%</span>
                            </div>
                            <div class="flex justify-between" style="display: flex; justify-content: space-between; align-items: center; direction: rtl; padding: 0.5rem 0;">
                                <span class="font-semibold" style="font-family: 'Tajawal', sans-serif; font-weight: 600;">تحسين التكلفة:</span>
                                <span class="text-green-600 font-bold" style="font-weight: 700; color: #16a34a;">-40%</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-blue-100 rounded-lg" style="margin-top: 1.5rem; padding: 1rem; background-color: #dbeafe; border-radius: 0.5rem;">
                            <p class="text-sm text-blue-800 font-semibold" style="font-size: 0.875rem; color: #1e40af; font-weight: 600; text-align: right; font-family: 'Tajawal', sans-serif;">✓ الأفضل لـ: تحسين جميع طرق التنظيف</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Case Studies Section -->
    <section id="case-studies" class="section-padding bg-gray-50">
        <div class="max-w-7xl mx-auto container-spacing">
            <!-- English Case Studies Header -->
            <div class="lang-content active text-center mb-20" data-lang="en">
                <h2 class="font-heading mb-6">Proven Success Stories</h2>
                <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                    Real results from leading solar installations across the Middle East and beyond.
                    See how our AI-powered solutions transformed their operations.
                </p>
            </div>

            <!-- Arabic Case Studies Header -->
            <div class="lang-content font-arabic text-center mb-20" data-lang="ar">
                <h2 class="font-heading mb-6">قصص نجاح مثبتة</h2>
                <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                    نتائج حقيقية من أبرز تركيبات الطاقة الشمسية في الشرق الأوسط وخارجه.
                    اكتشف كيف حولت حلولنا المدعومة بالذكاء الاصطناعي عملياتهم.
                </p>
            </div>

            <div class="grid lg:grid-cols-3 gap-8">
                <!-- Case Study 1: NEOM Solar Project -->
                <div class="card-enhanced">
                    <div class="mb-6">
                        <div class="w-full h-48 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center">
                            <div class="text-white text-center">
                                <div class="text-4xl mb-2">🏗️</div>
                                <div class="text-xl font-bold">NEOM Solar Project</div>
                                <div class="text-sm opacity-90">500 MW Installation</div>
                            </div>
                        </div>
                    </div>

                    <!-- English Content -->
                    <div class="lang-content active" data-lang="en">
                        <h3 class="font-heading mb-4">NEOM Mega Solar Project</h3>
                        <p class="text-gray-600 mb-6">
                            500 MW installation in Saudi Arabia's NEOM city achieved remarkable efficiency improvements
                            with our multi-method cleaning approach.
                        </p>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">Efficiency Improvement:</span>
                                <span class="text-green-600 font-bold">+28%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">Water Savings:</span>
                                <span class="text-blue-600 font-bold">92%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">Annual Cost Savings:</span>
                                <span class="text-green-600 font-bold">$2.8M</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">ROI Period:</span>
                                <span class="text-purple-600 font-bold">14 months</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-green-50 rounded-lg">
                            <p class="text-sm text-green-800 font-semibold">
                                "Revolutionary technology that exceeded all our expectations. The AI-powered scheduling
                                optimized our operations beyond what we thought possible." - Project Director
                            </p>
                        </div>
                    </div>

                    <!-- Arabic Content -->
                    <div class="lang-content font-arabic" data-lang="ar">
                        <h3 class="font-heading mb-4">مشروع نيوم الضخم للطاقة الشمسية</h3>
                        <p class="text-gray-600 mb-6">
                            تركيب 500 ميجاوات في مدينة نيوم السعودية حقق تحسينات كفاءة رائعة
                            بنهجنا متعدد الطرق للتنظيف.
                        </p>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">تحسن الكفاءة:</span>
                                <span class="text-green-600 font-bold">+28%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">توفير المياه:</span>
                                <span class="text-blue-600 font-bold">92%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">التوفير السنوي:</span>
                                <span class="text-green-600 font-bold">10.5 مليون ريال</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">فترة الاسترداد:</span>
                                <span class="text-purple-600 font-bold">14 شهر</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-green-50 rounded-lg">
                            <p class="text-sm text-green-800 font-semibold">
                                "تقنية ثورية فاقت كل توقعاتنا. الجدولة المدعومة بالذكاء الاصطناعي
                                حسنت عملياتنا بما يفوق ما اعتقدنا أنه ممكن." - مدير المشروع
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Case Study 2: Dubai Solar Park -->
                <div class="card-enhanced">
                    <div class="mb-6">
                        <div class="w-full h-48 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center">
                            <div class="text-white text-center">
                                <div class="text-4xl mb-2">🌆</div>
                                <div class="text-xl font-bold">Dubai Solar Park</div>
                                <div class="text-sm opacity-90">200 MW Installation</div>
                            </div>
                        </div>
                    </div>

                    <!-- English Content -->
                    <div class="lang-content active" data-lang="en">
                        <h3 class="font-heading mb-4">Dubai Solar Park Phase IV</h3>
                        <p class="text-gray-600 mb-6">
                            200 MW section of Dubai's massive solar park implemented our electrostatic cleaning
                            technology with outstanding results in the desert environment.
                        </p>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">Dust Reduction:</span>
                                <span class="text-green-600 font-bold">89%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">Maintenance Costs:</span>
                                <span class="text-blue-600 font-bold">-65%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">Energy Output:</span>
                                <span class="text-green-600 font-bold">+22%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">Water Eliminated:</span>
                                <span class="text-purple-600 font-bold">100%</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-orange-50 rounded-lg">
                            <p class="text-sm text-orange-800 font-semibold">
                                "The electrostatic technology is perfect for our desert conditions. Zero water usage
                                and maximum efficiency - exactly what we needed." - Operations Manager
                            </p>
                        </div>
                    </div>

                    <!-- Arabic Content -->
                    <div class="lang-content font-arabic" data-lang="ar">
                        <h3 class="font-heading mb-4">مجمع محمد بن راشد للطاقة الشمسية</h3>
                        <p class="text-gray-600 mb-6">
                            قسم 200 ميجاوات من مجمع دبي الضخم للطاقة الشمسية طبق تقنيتنا الكهروستاتيكية
                            للتنظيف بنتائج متميزة في البيئة الصحراوية.
                        </p>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">تقليل الغبار:</span>
                                <span class="text-green-600 font-bold">89%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">تكاليف الصيانة:</span>
                                <span class="text-blue-600 font-bold">-65%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">إنتاج الطاقة:</span>
                                <span class="text-green-600 font-bold">+22%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">إلغاء المياه:</span>
                                <span class="text-purple-600 font-bold">100%</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-orange-50 rounded-lg">
                            <p class="text-sm text-orange-800 font-semibold">
                                "التقنية الكهروستاتيكية مثالية لظروفنا الصحراوية. صفر استهلاك مياه
                                وأقصى كفاءة - بالضبط ما احتجناه." - مدير العمليات
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Case Study 3: Jordan Solar Farm -->
                <div class="card-enhanced">
                    <div class="mb-6">
                        <div class="w-full h-48 bg-gradient-to-br from-purple-400 to-indigo-500 rounded-lg flex items-center justify-center">
                            <div class="text-white text-center">
                                <div class="text-4xl mb-2">⛰️</div>
                                <div class="text-xl font-bold">Jordan Solar Farm</div>
                                <div class="text-sm opacity-90">150 MW Installation</div>
                            </div>
                        </div>
                    </div>

                    <!-- English Content -->
                    <div class="lang-content active" data-lang="en">
                        <h3 class="font-heading mb-4">Jordan Desert Solar Farm</h3>
                        <p class="text-gray-600 mb-6">
                            150 MW installation in Jordan's challenging desert environment achieved exceptional
                            performance with our AI-powered predictive maintenance system.
                        </p>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">Uptime Improvement:</span>
                                <span class="text-green-600 font-bold">99.7%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">Predictive Accuracy:</span>
                                <span class="text-blue-600 font-bold">96%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">Cost Reduction:</span>
                                <span class="text-green-600 font-bold">-58%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">Payback Period:</span>
                                <span class="text-purple-600 font-bold">16 months</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-purple-50 rounded-lg">
                            <p class="text-sm text-purple-800 font-semibold">
                                "The AI system predicts maintenance needs with incredible accuracy. We've eliminated
                                unexpected downtime completely." - Technical Director
                            </p>
                        </div>
                    </div>

                    <!-- Arabic Content -->
                    <div class="lang-content font-arabic" data-lang="ar">
                        <h3 class="font-heading mb-4">مزرعة الأردن الشمسية الصحراوية</h3>
                        <p class="text-gray-600 mb-6">
                            تركيب 150 ميجاوات في البيئة الصحراوية الصعبة بالأردن حقق أداءً استثنائياً
                            بنظام الصيانة التنبؤية المدعوم بالذكاء الاصطناعي.
                        </p>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">تحسن وقت التشغيل:</span>
                                <span class="text-green-600 font-bold">99.7%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">دقة التنبؤ:</span>
                                <span class="text-blue-600 font-bold">96%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">تقليل التكلفة:</span>
                                <span class="text-green-600 font-bold">-58%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-semibold">فترة الاسترداد:</span>
                                <span class="text-purple-600 font-bold">16 شهر</span>
                            </div>
                        </div>
                        <div class="mt-6 p-4 bg-purple-50 rounded-lg">
                            <p class="text-sm text-purple-800 font-semibold">
                                "نظام الذكاء الاصطناعي يتنبأ باحتياجات الصيانة بدقة مذهلة. ألغينا
                                التوقف غير المتوقع تماماً." - المدير التقني
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="section-padding bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto container-spacing">
            <!-- English Contact Header -->
            <div class="lang-content active text-center mb-20" data-lang="en">
                <h2 class="font-heading mb-6 text-white">Ready to Transform Your Solar Operations?</h2>
                <p class="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                    Join leading solar installations worldwide. Get started with a free consultation and
                    discover how AI can revolutionize your solar panel maintenance.
                </p>
            </div>

            <!-- Arabic Contact Header -->
            <div class="lang-content font-arabic text-center mb-20" data-lang="ar">
                <h2 class="font-heading mb-6 text-white">هل أنت مستعد لتحويل عمليات الطاقة الشمسية لديك؟</h2>
                <p class="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                    انضم إلى أبرز تركيبات الطاقة الشمسية عالمياً. ابدأ بالحصول على استشارة مجانية
                    واكتشف كيف يمكن للذكاء الاصطناعي أن يحدث ثورة في صيانة الألواح الشمسية لديك.
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-16">
                <!-- Contact Information -->
                <div>
                    <!-- English Contact Info -->
                    <div class="lang-content active" data-lang="en">
                        <h3 class="text-2xl font-bold mb-8 text-white">Get In Touch</h3>
                        <div class="space-y-6">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                                    <span class="text-white text-xl">📧</span>
                                </div>
                                <div>
                                    <div class="font-semibold text-white">Email</div>
                                    <div class="text-gray-300"><EMAIL></div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                                    <span class="text-white text-xl">📱</span>
                                </div>
                                <div>
                                    <div class="font-semibold text-white">Phone</div>
                                    <div class="text-gray-300">+966 50 123 4567</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                                    <span class="text-white text-xl">🌐</span>
                                </div>
                                <div>
                                    <div class="font-semibold text-white">Website</div>
                                    <div class="text-gray-300">www.solar-ai-monitoring.com</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center">
                                    <span class="text-white text-xl">📍</span>
                                </div>
                                <div>
                                    <div class="font-semibold text-white">Location</div>
                                    <div class="text-gray-300">Riyadh, Saudi Arabia</div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-12 p-8 bg-gradient-to-r from-blue-600 to-green-600 rounded-xl">
                            <h4 class="text-xl font-bold mb-6 text-white">Why Choose Solar AI?</h4>
                            <ul class="space-y-3">
                                <li class="flex items-center space-x-3">
                                    <span class="text-green-300 text-lg">✓</span>
                                    <span class="text-white">99% AI Detection Accuracy</span>
                                </li>
                                <li class="flex items-center space-x-3">
                                    <span class="text-green-300 text-lg">✓</span>
                                    <span class="text-white">50% Cost Reduction Guaranteed</span>
                                </li>
                                <li class="flex items-center space-x-3">
                                    <span class="text-green-300 text-lg">✓</span>
                                    <span class="text-white">85% Water Savings</span>
                                </li>
                                <li class="flex items-center space-x-3">
                                    <span class="text-green-300 text-lg">✓</span>
                                    <span class="text-white">18-Month ROI Payback</span>
                                </li>
                                <li class="flex items-center space-x-3">
                                    <span class="text-green-300 text-lg">✓</span>
                                    <span class="text-white">24/7 Expert Support</span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Arabic Contact Info -->
                    <div class="lang-content font-arabic" data-lang="ar">
                        <h3 class="text-2xl font-bold mb-8 text-white">تواصل معنا</h3>
                        <div class="space-y-6">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                                    <span class="text-white text-xl">📧</span>
                                </div>
                                <div>
                                    <div class="font-semibold text-white">البريد الإلكتروني</div>
                                    <div class="text-gray-300"><EMAIL></div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                                    <span class="text-white text-xl">📱</span>
                                </div>
                                <div>
                                    <div class="font-semibold text-white">الهاتف</div>
                                    <div class="text-gray-300">+966 50 123 4567</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                                    <span class="text-white text-xl">🌐</span>
                                </div>
                                <div>
                                    <div class="font-semibold text-white">الموقع الإلكتروني</div>
                                    <div class="text-gray-300">www.solar-ai-monitoring.com</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center">
                                    <span class="text-white text-xl">📍</span>
                                </div>
                                <div>
                                    <div class="font-semibold text-white">الموقع</div>
                                    <div class="text-gray-300">الرياض، المملكة العربية السعودية</div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-12 p-8 bg-gradient-to-r from-blue-600 to-green-600 rounded-xl">
                            <h4 class="text-xl font-bold mb-6 text-white">لماذا تختار Solar AI؟</h4>
                            <ul class="space-y-3">
                                <li class="flex items-center space-x-3">
                                    <span class="text-green-300 text-lg">✓</span>
                                    <span class="text-white">دقة كشف 99% بالذكاء الاصطناعي</span>
                                </li>
                                <li class="flex items-center space-x-3">
                                    <span class="text-green-300 text-lg">✓</span>
                                    <span class="text-white">تقليل التكاليف 50% مضمون</span>
                                </li>
                                <li class="flex items-center space-x-3">
                                    <span class="text-green-300 text-lg">✓</span>
                                    <span class="text-white">توفير المياه 85%</span>
                                </li>
                                <li class="flex items-center space-x-3">
                                    <span class="text-green-300 text-lg">✓</span>
                                    <span class="text-white">استرداد الاستثمار خلال 18 شهر</span>
                                </li>
                                <li class="flex items-center space-x-3">
                                    <span class="text-green-300 text-lg">✓</span>
                                    <span class="text-white">دعم خبراء 24/7</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div>
                    <!-- English Form -->
                    <div class="lang-content active bg-gray-800 rounded-2xl p-8" data-lang="en">
                        <h3 class="text-2xl font-bold mb-8 text-white text-center">🚀 Get Your Free Demo</h3>
                        <form id="contact-form-en" class="space-y-6" onsubmit="submitForm(event, 'en')">
                            <div>
                                <label class="block text-white font-semibold mb-2">Full Name *</label>
                                <input type="text" name="name" required class="form-input" placeholder="Enter your full name">
                            </div>
                            <div>
                                <label class="block text-white font-semibold mb-2">Email Address *</label>
                                <input type="email" name="email" required class="form-input" placeholder="<EMAIL>">
                            </div>
                            <div>
                                <label class="block text-white font-semibold mb-2">Company Name</label>
                                <input type="text" name="company" class="form-input" placeholder="Your company name">
                            </div>
                            <div>
                                <label class="block text-white font-semibold mb-2">Installation Size</label>
                                <select name="size" class="form-input">
                                    <option value="">Select installation size</option>
                                    <option value="1-10">1-10 MW</option>
                                    <option value="10-50">10-50 MW</option>
                                    <option value="50-100">50-100 MW</option>
                                    <option value="100+">100+ MW</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-white font-semibold mb-2">Message</label>
                                <textarea name="message" rows="4" class="form-input" placeholder="Tell us about your solar installation and challenges..."></textarea>
                            </div>
                            <button type="submit" class="w-full btn-primary text-lg py-4 relative">
                                <span class="btn-text">🎯 Schedule Free Demo</span>
                            </button>
                        </form>
                        <div id="success-message-en" class="success-message">
                            <strong>Thank you!</strong> Your demo request has been submitted successfully. Our team will contact you within 24 hours.
                        </div>
                    </div>

                    <!-- Arabic Form -->
                    <div class="lang-content font-arabic bg-gray-800 rounded-2xl p-8" data-lang="ar" style="direction: rtl; text-align: right;">
                        <h3 class="text-2xl font-bold mb-8 text-white text-center" style="font-size: 1.875rem; font-weight: 700; margin-bottom: 2rem; text-align: center; color: white; font-family: 'Cairo', sans-serif;">🚀 احصل على عرضك التوضيحي المجاني</h3>
                        <form id="contact-form-ar" class="space-y-6" onsubmit="submitForm(event, 'ar')" style="direction: rtl;">
                            <div style="margin-bottom: 1.5rem;">
                                <label class="block text-white font-semibold mb-2" style="display: block; color: white; font-weight: 600; margin-bottom: 0.5rem; text-align: right; font-family: 'Tajawal', sans-serif;">الاسم الكامل *</label>
                                <input type="text" name="name" required class="form-input" placeholder="أدخل اسمك الكامل" style="width: 100%; padding: 1rem; border-radius: 0.75rem; border: 2px solid #e5e7eb; text-align: right; direction: rtl; font-family: 'Tajawal', sans-serif;">
                            </div>
                            <div style="margin-bottom: 1.5rem;">
                                <label class="block text-white font-semibold mb-2" style="display: block; color: white; font-weight: 600; margin-bottom: 0.5rem; text-align: right; font-family: 'Tajawal', sans-serif;">البريد الإلكتروني *</label>
                                <input type="email" name="email" required class="form-input" placeholder="<EMAIL>" style="width: 100%; padding: 1rem; border-radius: 0.75rem; border: 2px solid #e5e7eb; text-align: right; direction: rtl; font-family: 'Tajawal', sans-serif;">
                            </div>
                            <div style="margin-bottom: 1.5rem;">
                                <label class="block text-white font-semibold mb-2" style="display: block; color: white; font-weight: 600; margin-bottom: 0.5rem; text-align: right; font-family: 'Tajawal', sans-serif;">اسم الشركة</label>
                                <input type="text" name="company" class="form-input" placeholder="اسم شركتك" style="width: 100%; padding: 1rem; border-radius: 0.75rem; border: 2px solid #e5e7eb; text-align: right; direction: rtl; font-family: 'Tajawal', sans-serif;">
                            </div>
                            <div style="margin-bottom: 1.5rem;">
                                <label class="block text-white font-semibold mb-2" style="display: block; color: white; font-weight: 600; margin-bottom: 0.5rem; text-align: right; font-family: 'Tajawal', sans-serif;">حجم التركيب</label>
                                <select name="size" class="form-input" style="width: 100%; padding: 1rem; border-radius: 0.75rem; border: 2px solid #e5e7eb; text-align: right; direction: rtl; font-family: 'Tajawal', sans-serif;">
                                    <option value="">اختر حجم التركيب</option>
                                    <option value="1-10">1-10 ميجاوات</option>
                                    <option value="10-50">10-50 ميجاوات</option>
                                    <option value="50-100">50-100 ميجاوات</option>
                                    <option value="100+">100+ ميجاوات</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 1.5rem;">
                                <label class="block text-white font-semibold mb-2" style="display: block; color: white; font-weight: 600; margin-bottom: 0.5rem; text-align: right; font-family: 'Tajawal', sans-serif;">الرسالة</label>
                                <textarea name="message" rows="4" class="form-input" placeholder="أخبرنا عن تركيب الطاقة الشمسية والتحديات التي تواجهها..." style="width: 100%; padding: 1rem; border-radius: 0.75rem; border: 2px solid #e5e7eb; text-align: right; direction: rtl; font-family: 'Tajawal', sans-serif; resize: vertical;"></textarea>
                            </div>
                            <button type="submit" class="w-full btn-primary text-lg py-4 relative" style="width: 100%; padding: 1rem 2rem; font-size: 1.125rem; font-weight: 600; text-align: center; font-family: 'Tajawal', sans-serif;">
                                <span class="btn-text">🎯 جدولة عرض توضيحي مجاني</span>
                            </button>
                        </form>
                        <div id="success-message-ar" class="success-message" style="text-align: right; font-family: 'Tajawal', sans-serif;">
                            <strong>شكراً لك!</strong> تم إرسال طلب العرض التوضيحي بنجاح. سيتواصل معك فريقنا خلال 24 ساعة.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-black text-white py-16">
        <div class="max-w-7xl mx-auto container-spacing">
            <div class="text-center">
                <div class="flex items-center justify-center mb-8">
                    <div class="w-16 h-16 gradient-primary rounded-xl flex items-center justify-center">
                        <span class="text-white font-bold text-3xl">☀️</span>
                    </div>
                    <span class="ml-4 text-3xl font-bold font-heading">Solar AI</span>
                </div>

                <!-- English Footer -->
                <div class="lang-content active" data-lang="en">
                    <p class="text-xl text-gray-400 mb-8 max-w-2xl mx-auto">
                        Revolutionizing Solar Panel Maintenance with AI-Powered Technology
                    </p>
                    <div class="flex justify-center space-x-12 mb-8 text-lg">
                        <span class="text-blue-400 font-semibold">🎯 99% AI Accuracy</span>
                        <span class="text-green-400 font-semibold">💰 50% Cost Reduction</span>
                        <span class="text-orange-400 font-semibold">💧 85% Water Savings</span>
                    </div>
                </div>

                <!-- Arabic Footer -->
                <div class="lang-content font-arabic" data-lang="ar" style="direction: rtl; text-align: center;">
                    <p class="text-xl text-gray-400 mb-8 max-w-2xl mx-auto" style="font-size: 1.125rem; color: #9ca3af; margin-bottom: 2rem; max-width: 42rem; margin-left: auto; margin-right: auto; text-align: center; font-family: 'Tajawal', sans-serif; line-height: 1.7;">
                        ثورة في صيانة الألواح الشمسية بتقنيات الذكاء الاصطناعي المتطورة
                    </p>
                    <div class="flex justify-center space-x-12 mb-8 text-lg" style="display: flex; justify-content: center; gap: 2rem; margin-bottom: 2rem; font-size: 1rem; direction: rtl; flex-wrap: wrap;">
                        <span class="text-blue-400 font-semibold" style="color: #60a5fa; font-weight: 600; font-family: 'Tajawal', sans-serif; margin: 0.5rem;">🎯 دقة 99% بالذكاء الاصطناعي</span>
                        <span class="text-green-400 font-semibold" style="color: #4ade80; font-weight: 600; font-family: 'Tajawal', sans-serif; margin: 0.5rem;">💰 تقليل التكاليف 50%</span>
                        <span class="text-orange-400 font-semibold" style="color: #fb923c; font-weight: 600; font-family: 'Tajawal', sans-serif; margin: 0.5rem;">💧 توفير المياه 85%</span>
                    </div>
                </div>

                <div class="border-t border-gray-800 pt-8">
                    <p class="text-gray-500 text-sm">© 2025 Solar AI Cleaning & Monitoring System. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Language Management
        let currentLanguage = localStorage.getItem('preferred-language') || 'en';

        // Initialize language on page load with professional setup
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth page transition
            document.body.style.transition = 'all 0.3s ease';

            // Initialize with stored language or default to English
            const storedLang = localStorage.getItem('preferred-language') || 'en';
            currentLanguage = storedLang;

            // Set initial language without animation
            setInitialLanguage(storedLang);

            // Initialize other functions
            updateNavbarOnScroll();
            initializeAnimations();
        });

        // Set initial language without transition effects
        function setInitialLanguage(lang) {
            const body = document.body;
            const html = document.documentElement;
            const currentFlag = document.getElementById('current-flag');
            const currentLangText = document.getElementById('current-lang');

            // Set language attributes and classes
            if (lang === 'ar') {
                if (currentFlag) currentFlag.textContent = '🇸🇦';
                if (currentLangText) currentLangText.textContent = 'AR';
                body.classList.add('rtl', 'font-arabic');
                body.style.direction = 'rtl';
                body.style.textAlign = 'right';
                html.setAttribute('lang', 'ar');
                html.setAttribute('dir', 'rtl');

                // Apply Arabic-specific styling
                const logoContainer = document.querySelector('.logo-container');
                if (logoContainer) {
                    logoContainer.style.flexDirection = 'row-reverse';
                }

                // Update navigation styling
                const desktopNav = document.querySelector('.desktop-nav');
                if (desktopNav) {
                    desktopNav.style.direction = 'rtl';
                    desktopNav.style.gap = '2rem';
                }
            } else {
                if (currentFlag) currentFlag.textContent = '🇺🇸';
                if (currentLangText) currentLangText.textContent = 'EN';
                body.classList.remove('rtl', 'font-arabic');
                body.style.direction = 'ltr';
                body.style.textAlign = 'left';
                html.setAttribute('lang', 'en');
                html.setAttribute('dir', 'ltr');

                // Reset to English styling
                const logoContainer = document.querySelector('.logo-container');
                if (logoContainer) {
                    logoContainer.style.flexDirection = 'row';
                }

                // Reset navigation styling
                const desktopNav = document.querySelector('.desktop-nav');
                if (desktopNav) {
                    desktopNav.style.direction = 'ltr';
                    desktopNav.style.gap = '1.5rem';
                }
            }

            // Show/hide content without animation
            document.querySelectorAll('.lang-content').forEach(content => {
                content.classList.remove('active');
                content.style.display = 'none';
            });

            document.querySelectorAll(`[data-lang="${lang}"]`).forEach(content => {
                content.classList.add('active');
                if (content.classList.contains('flex')) {
                    content.style.display = 'flex';
                } else if (content.classList.contains('grid')) {
                    content.style.display = 'grid';
                } else {
                    content.style.display = 'block';
                }
                content.style.opacity = '1';
                content.style.transform = 'translateY(0)';
            });

            // Update dropdown active state
            document.querySelectorAll('.language-option').forEach(option => {
                option.classList.remove('active');
            });
            const targetOption = document.querySelector(`[onclick="switchLanguage('${lang}')"]`);
            if (targetOption) {
                targetOption.classList.add('active');
            }

            // Update meta tags
            updateMetaTags(lang);
        }

        function toggleLanguageDropdown() {
            const dropdown = document.getElementById('language-dropdown');
            const arrow = document.getElementById('dropdown-arrow');
            dropdown.classList.toggle('active');
            arrow.style.transform = dropdown.classList.contains('active') ? 'rotate(180deg)' : 'rotate(0deg)';
        }

        function switchLanguage(lang) {
            console.log('Switching to language:', lang, 'Current:', currentLanguage);

            // Add smooth transition effect
            document.body.style.transition = 'all 0.3s ease';

            currentLanguage = lang;
            localStorage.setItem('preferred-language', lang);

            const body = document.body;
            const html = document.documentElement;
            const currentFlag = document.getElementById('current-flag');
            const currentLangText = document.getElementById('current-lang');
            const dropdown = document.getElementById('language-dropdown');

            // Hide dropdown with animation
            if (dropdown) {
                dropdown.classList.remove('active');
            }
            const dropdownArrow = document.getElementById('dropdown-arrow');
            if (dropdownArrow) {
                dropdownArrow.style.transform = 'rotate(0deg)';
            }

            // Fade out current content
            document.querySelectorAll('.lang-content.active').forEach(content => {
                content.style.opacity = '0';
                content.style.transform = 'translateY(10px)';
            });

            // Wait for fade out, then switch content
            setTimeout(() => {
                // Update language display and body classes
                if (lang === 'ar') {
                    if (currentFlag) currentFlag.textContent = '🇸🇦';
                    if (currentLangText) currentLangText.textContent = 'AR';
                    body.classList.add('rtl', 'font-arabic');
                    body.style.direction = 'rtl';
                    body.style.textAlign = 'right';
                    html.setAttribute('lang', 'ar');
                    html.setAttribute('dir', 'rtl');

                    // Apply Arabic-specific styling
                    const logoContainer = document.querySelector('.logo-container');
                    if (logoContainer) {
                        logoContainer.style.flexDirection = 'row-reverse';
                    }

                    // Update navigation spacing for Arabic
                    const navItems = document.querySelectorAll('.lang-content[data-lang="ar"] .space-x-6');
                    navItems.forEach(nav => {
                        nav.style.gap = '2rem';
                        nav.style.direction = 'rtl';
                    });

                    const desktopNav = document.querySelector('.desktop-nav');
                    if (desktopNav) {
                        desktopNav.style.direction = 'rtl';
                        desktopNav.style.gap = '2rem';
                    }
                } else {
                    if (currentFlag) currentFlag.textContent = '🇺🇸';
                    if (currentLangText) currentLangText.textContent = 'EN';
                    body.classList.remove('rtl', 'font-arabic');
                    body.style.direction = 'ltr';
                    body.style.textAlign = 'left';
                    html.setAttribute('lang', 'en');
                    html.setAttribute('dir', 'ltr');

                    // Reset to English styling
                    const logoContainer = document.querySelector('.logo-container');
                    if (logoContainer) {
                        logoContainer.style.flexDirection = 'row';
                    }

                    // Reset navigation spacing for English
                    const navItems = document.querySelectorAll('.lang-content[data-lang="en"] .space-x-6');
                    navItems.forEach(nav => {
                        nav.style.gap = '1.5rem';
                        nav.style.direction = 'ltr';
                    });

                    const desktopNav = document.querySelector('.desktop-nav');
                    if (desktopNav) {
                        desktopNav.style.direction = 'ltr';
                        desktopNav.style.gap = '1.5rem';
                    }
                }

                // Hide all language content
                document.querySelectorAll('.lang-content').forEach(content => {
                    content.classList.remove('active');
                    content.style.display = 'none';
                });

                // Show content for selected language with fade in
                document.querySelectorAll(`[data-lang="${lang}"]`).forEach(content => {
                    content.classList.add('active');

                    // Restore proper display based on original classes
                    if (content.classList.contains('flex')) {
                        content.style.display = 'flex';
                    } else if (content.classList.contains('grid')) {
                        content.style.display = 'grid';
                    } else {
                        content.style.display = 'block';
                    }

                    // Fade in animation
                    content.style.opacity = '0';
                    content.style.transform = 'translateY(10px)';

                    setTimeout(() => {
                        content.style.transition = 'all 0.3s ease';
                        content.style.opacity = '1';
                        content.style.transform = 'translateY(0)';
                    }, 50);
                });

                // Update language dropdown active state
                document.querySelectorAll('.language-option').forEach(option => {
                    option.classList.remove('active');
                });

                // Find and activate the correct language option
                const targetOption = document.querySelector(`[onclick="switchLanguage('${lang}')"]`);
                if (targetOption) {
                    targetOption.classList.add('active');
                }

                // Update meta tags
                updateMetaTags(lang);

                console.log('Language switched successfully to:', lang);
            }, 150);
        }

        function updateMetaTags(lang) {
            const title = document.querySelector('title');
            const description = document.querySelector('meta[name="description"]');
            const keywords = document.querySelector('meta[name="keywords"]');

            // Update title
            if (title) {
                if (lang === 'ar') {
                    title.textContent = 'نظام مراقبة وتنظيف الألواح الشمسية بالذكاء الاصطناعي - صيانة ثورية للألواح الشمسية';
                } else {
                    title.textContent = 'Solar AI Cleaning & Monitoring System - Revolutionary Solar Panel Maintenance';
                }
            }

            // Update description
            if (description) {
                if (lang === 'ar') {
                    description.setAttribute('content', 'نظام ثوري لمراقبة وتنظيف الألواح الشمسية بالذكاء الاصطناعي. احصل على دقة كشف 99%، وقلل التكاليف بنسبة 50%، ووفر 85% من استهلاك المياه.');
                } else {
                    description.setAttribute('content', 'Revolutionary AI-powered solar panel cleaning and monitoring system. Achieve 99% detection accuracy, reduce costs by 50%, and save 85% water consumption.');
                }
            }

            // Update keywords
            if (keywords) {
                if (lang === 'ar') {
                    keywords.setAttribute('content', 'الذكاء الاصطناعي، الألواح الشمسية، التنظيف، المراقبة، الطاقة المتجددة، الصيانة');
                } else {
                    keywords.setAttribute('content', 'AI, solar panels, cleaning, monitoring, renewable energy, maintenance, automation');
                }
            }
        }

        // Mobile Menu Toggle
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        }

        // Smooth Scrolling
        function scrollToSection(sectionId) {
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // Enhanced smooth scrolling for all anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                scrollToSection(targetId);
            });
        });

        // Navbar scroll effect
        function updateNavbarOnScroll() {
            const navbar = document.getElementById('navbar');

            window.addEventListener('scroll', function() {
                if (window.scrollY > 100) {
                    navbar.classList.add('bg-white/95', 'backdrop-blur-lg', 'shadow-xl');
                } else {
                    navbar.classList.remove('bg-white/95', 'backdrop-blur-lg', 'shadow-xl');
                }
            });
        }

        // Form Submission
        async function submitForm(event, lang) {
            event.preventDefault();

            const form = event.target;
            const submitBtn = form.querySelector('button[type="submit"]');
            const btnText = submitBtn.querySelector('.btn-text');
            const successMessage = document.getElementById(`success-message-${lang}`);

            // Show loading state
            submitBtn.classList.add('loading');
            btnText.textContent = lang === 'ar' ? 'جاري الإرسال...' : 'Sending...';
            submitBtn.disabled = true;

            // Simulate form submission (replace with actual API call)
            try {
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Show success message
                successMessage.classList.add('show');
                form.reset();

                // Hide success message after 5 seconds
                setTimeout(() => {
                    successMessage.classList.remove('show');
                }, 5000);

            } catch (error) {
                console.error('Form submission error:', error);
                alert(lang === 'ar' ? 'حدث خطأ. يرجى المحاولة مرة أخرى.' : 'An error occurred. Please try again.');
            } finally {
                // Reset button state
                submitBtn.classList.remove('loading');
                btnText.textContent = lang === 'ar' ? '🎯 جدولة عرض توضيحي مجاني' : '🎯 Schedule Free Demo';
                submitBtn.disabled = false;
            }
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const languageSwitcher = document.querySelector('.language-switcher');
            const dropdown = document.getElementById('language-dropdown');

            if (languageSwitcher && !languageSwitcher.contains(event.target)) {
                dropdown.classList.remove('active');
                document.getElementById('dropdown-arrow').style.transform = 'rotate(0deg)';
            }
        });

        // Initialize animations
        function initializeAnimations() {
            // Intersection Observer for fade-in animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in');
                    }
                });
            }, observerOptions);

            // Observe all cards and sections
            document.querySelectorAll('.card-enhanced, .stat-card').forEach(el => {
                observer.observe(el);
            });
        }

        // Performance optimization: Lazy load images
        function lazyLoadImages() {
            const images = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        }

        // Initialize lazy loading
        document.addEventListener('DOMContentLoaded', lazyLoadImages);

        // Add keyboard navigation support
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const dropdown = document.getElementById('language-dropdown');
                const mobileMenu = document.getElementById('mobile-menu');

                dropdown.classList.remove('active');
                mobileMenu.classList.add('hidden');
            }
        });

        // Add focus management for accessibility
        function manageFocus() {
            const focusableElements = document.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );

            focusableElements.forEach(element => {
                element.addEventListener('focus', function() {
                    this.style.outline = '2px solid #3b82f6';
                    this.style.outlineOffset = '2px';
                });

                element.addEventListener('blur', function() {
                    this.style.outline = 'none';
                });
            });
        }

        // Initialize focus management
        document.addEventListener('DOMContentLoaded', manageFocus);
    </script>
</body>
</html>
